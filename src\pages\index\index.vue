<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 今日密码数据
const todayPasswords = ref([
  { name: '航天基地', code: '1709', date: '2025-07-13', color: '#ff6b9d' },
  { name: '潮汐监狱', code: '4896', date: '2025-07-13', color: '#ff6b9d' },
  { name: '零号大坝', code: '7607', date: '2025-07-13', color: '#00d4aa' },
  { name: '长弓溪谷', code: '9332', date: '2025-07-13', color: '#00bcd4' },
  { name: '巴克什', code: '5741', date: '2025-07-13', color: '#9c27b0' },
])

// 功能按钮数据
const functionButtons = ref([
  {
    title: '鼠鼠开容器',
    subtitle: '鼠鼠偷吃模拟器',
    color: 'linear-gradient(135deg, #ff6b9d, #ff8a80)',
    route: '/pages/drawCard/debug',
  },
  {
    title: '鼠鼠卡战备',
    subtitle: '机密，绝密，以小博大',
    color: 'linear-gradient(135deg, #9c27b0, #ba68c8)',
    route: '/pages/drawCard/index',
  },
  {
    title: 'S5赛季任务',
    subtitle: '看看赛季任务吧',
    color: 'linear-gradient(135deg, #e91e63, #f06292)',
    route: '/pages/drawCard/index',
  },
  {
    title: '特勤处收益',
    subtitle: '看看哪个制造好点',
    color: 'linear-gradient(135deg, #9c27b0, #ba68c8)',
    route: '/pages/drawCard/index',
  },
  {
    title: '今日钥匙卡',
    subtitle: '今日钥匙卡货预测',
    color: 'linear-gradient(135deg, #e91e63, #f06292)',
    route: '/pages/drawCard/index',
  },
  {
    title: '今日子弹',
    subtitle: '今日子弹补货预测',
    color: 'linear-gradient(135deg, #e91e63, #f06292)',
    route: '/pages/drawCard/index',
  },
])

// 跳转到功能页面
function navigateToPage(route: string) {
  uni.navigateTo({ url: route })
}

onLoad(() => {
  console.log('主页加载完成')
})
</script>

<template>
  <view class="min-h-screen from-purple-100 to-white bg-gradient-to-b" :style="{ paddingTop: `${safeAreaInsets?.top}px` }">
    <!-- 顶部标题 -->
    <view class="py-6 text-center">
      <text class="text-2xl text-purple-600 font-bold">
        今日密码
      </text>
    </view>

    <!-- 密码卡片区域 -->
    <view class="mb-6 px-4">
      <view class="rounded-2xl bg-white p-4 shadow-lg">
        <!-- 第一行：航天基地、潮汐监狱 -->
        <view class="mb-4 flex justify-between">
          <view class="mr-2 flex-1">
            <view class="text-center">
              <text class="mb-1 text-sm text-gray-600">
                {{ todayPasswords[0].name }}
              </text>
              <text class="mb-1 text-3xl font-bold" :style="{ color: todayPasswords[0].color }">
                {{ todayPasswords[0].code }}
              </text>
              <text class="text-xs text-gray-400">
                {{ todayPasswords[0].date }}
              </text>
            </view>
          </view>
          <view class="ml-2 flex-1">
            <view class="text-center">
              <text class="mb-1 text-sm text-gray-600">
                {{ todayPasswords[1].name }}
              </text>
              <text class="mb-1 text-3xl font-bold" :style="{ color: todayPasswords[1].color }">
                {{ todayPasswords[1].code }}
              </text>
              <text class="text-xs text-gray-400">
                {{ todayPasswords[1].date }}
              </text>
            </view>
          </view>
        </view>

        <!-- 第二行：零号大坝、长弓溪谷、巴克什 -->
        <view class="flex justify-between">
          <view class="mr-1 flex-1">
            <view class="text-center">
              <text class="mb-1 text-sm text-gray-600">
                {{ todayPasswords[2].name }}
              </text>
              <text class="mb-1 text-3xl font-bold" :style="{ color: todayPasswords[2].color }">
                {{ todayPasswords[2].code }}
              </text>
              <text class="text-xs text-gray-400">
                {{ todayPasswords[2].date }}
              </text>
            </view>
          </view>
          <view class="mx-1 flex-1">
            <view class="text-center">
              <text class="mb-1 text-sm text-gray-600">
                {{ todayPasswords[3].name }}
              </text>
              <text class="mb-1 text-3xl font-bold" :style="{ color: todayPasswords[3].color }">
                {{ todayPasswords[3].code }}
              </text>
              <text class="text-xs text-gray-400">
                {{ todayPasswords[3].date }}
              </text>
            </view>
          </view>
          <view class="ml-1 flex-1">
            <view class="text-center">
              <text class="mb-1 text-sm text-gray-600">
                {{ todayPasswords[4].name }}
              </text>
              <text class="mb-1 text-3xl font-bold" :style="{ color: todayPasswords[4].color }">
                {{ todayPasswords[4].code }}
              </text>
              <text class="text-xs text-gray-400">
                {{ todayPasswords[4].date }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="px-4 pb-20">
      <view class="grid grid-cols-2 gap-4">
        <view
          v-for="(item, index) in functionButtons"
          :key="index"
          class="flex items-center justify-between rounded-2xl p-4 shadow-lg"
          :style="{ background: item.color }"
          @click="navigateToPage(item.route)"
        >
          <view class="flex-1">
            <text class="mb-1 text-lg text-white font-bold">
              {{ item.title }}
            </text>
            <text class="text-sm text-white opacity-90">
              {{ item.subtitle }}
            </text>
          </view>
          <view class="ml-2">
            <text class="text-2xl text-white">
              🔍
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

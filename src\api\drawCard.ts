import { httpGet } from '@/http/http'

// 产品卡片接口类型定义
export interface IProduct {
  id?: string | number
  objectName: string
  prePic: string
  avgPrice: number | string // 可能是数字或字符串
  weight: number
  width: number
  length: number
  theme: string
}

// 抽卡响应类型
export interface IDrawResponse {
  cards: IProduct[]
  drawValue: number
  totalUserValue: number
  turntableSpins: number
  alreadyDrawn?: boolean // 可选字段
}

// 测试响应类型
export interface ITestResponse {
  numTests: number
  maxValue: number
  minValue: number
  averageValue: number
  drawValues: number[]
}

/**
 * 抽取卡片
 * @param userId 用户ID
 * @returns 抽卡结果
 */
export function drawCardsAPI(userId: string) {
  return httpGet<IDrawResponse>('/Mon/draw', { id: userId })
}

/**
 * 运行测试
 * @param userId 用户ID
 * @returns 测试结果
 */
export function runTestAPI(userId: string) {
  return httpGet<ITestResponse>('/Mon/test', { id: userId })
}

import { httpGet } from '@/http/http'

// 奖品接口类型定义
export interface IPrize {
  name: string
  value: number
}

// 抽奖结果接口类型定义
export interface ISpinResult {
  name: string
  value: number
  timestamp: string
}

// 获取奖品信息响应类型
export interface IPrizesResponse {
  prizes: IPrize[]
  remainingSpins: number
  spinResults?: ISpinResult[]
}

// 抽奖响应类型
export interface ISpinResponse {
  prize: IPrize
  prizeIndex: number
  remainingSpins: number
}

/**
 * 获取转盘奖品信息
 * @param userId 用户ID
 * @returns 奖品信息和剩余次数
 */
export function getTurntablePrizesAPI(userId: string) {
  return httpGet<IPrizesResponse>('/Mon/prizes', { id: userId })
}

/**
 * 执行转盘抽奖
 * @param userId 用户ID
 * @returns 抽奖结果
 */
export function spinTurntableAPI(userId: string) {
  return httpGet<ISpinResponse>('/Mon/spin', { id: userId })
}
